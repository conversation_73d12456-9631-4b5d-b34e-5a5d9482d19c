using System;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Builder;
using Serilog;
using Serilog.Sinks.MSSqlServer;
using Serilog.Events;
using WHO.MALARIA.Domain.Constants;
using System.IO;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Web.Extensions;
using WHO.MALARIA.Web.Middlewares;
using WHO.MALARIA.Web.PipelineBehaviours;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using SendGrid.Extensions.DependencyInjection;
using MediatR;

namespace WHO.MALARIA.Web
{
    public class Program
    {
        public static void Main(string[] args)
        {
            try
            {
                IConfiguration configuration = GetConfiguration();
                Log.Logger = CreateLogger(configuration);

                var builder = WebApplication.CreateBuilder(args);

                // Add configuration
                LoadConfiguration(builder.Configuration);

                // Add services to the container
                ConfigureServices(builder.Services, configuration, builder.Environment);

                // Configure Serilog
                builder.Host.UseSerilog();

                // Configure Kestrel
                builder.WebHost.ConfigureKestrel(opt =>
                {
                    opt.AddServerHeader = false;
                    opt.Limits.MaxRequestBodySize = null;
                    opt.Limits.KeepAliveTimeout = TimeSpan.FromMinutes(60);
                    opt.Limits.RequestHeadersTimeout = TimeSpan.FromMinutes(60);
                });

                var app = builder.Build();

                // Configure the HTTP request pipeline
                ConfigureApp(app);

                // Migrate and seed database
                // Commented out for development - uncomment when database is available
// app.Services.GetRequiredService<IHost>().MigrateDatabase().SeedData();

                Log.Information("Getting the Web Application running ({ApplicationContext})...", Constants.SeriLog.AppName);
                app.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Host terminated unexpectedly ({ApplicationContext})", Constants.SeriLog.AppName);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        private static void ConfigureServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment env)
        {
            var appSettingConfiguration = new AppSettings();
            configuration.Bind(Constants.Startup.AppSettings, appSettingConfiguration);

            //configure section of custom config json file
            services.Configure<DQAExcelSetting>(configuration.GetSection(DQAExcelSetting.DQASetting));
            services.Configure<QuestionBankExcelSetting>(configuration.GetSection(QuestionBankExcelSetting.QuestionBankSetting));
            services.Configure<AnalyticalOutputExcelSetting>(configuration.GetSection(AnalyticalOutputExcelSetting.AnalyticalOutputSetting));
            services.Configure<ShellTableExcelSetting>(configuration.GetSection(ShellTableExcelSetting.ShellTableSetting));
            services.AddMemoryCache();
            services.Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto |
                ForwardedHeaders.XForwardedHost;

                options.ForwardedHostHeaderName = "X-Original-Host";

                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();
            });

            services.AddSingleton(appSettingConfiguration);
            services.AddControllers().AddNewtonsoftJson(options =>
            {
                // Configure date handling for consistent parsing
                options.SerializerSettings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.IsoDateFormat;
                options.SerializerSettings.DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Utc;
                options.SerializerSettings.DateParseHandling = Newtonsoft.Json.DateParseHandling.DateTime;
            });
            services.AddMvc(options => { options.EnableEndpointRouting = true; });

            // adding swagger
            services.AddSwaggerDocumentation();

            services.AddHttpContextAccessor();

            // adding database context
            services.AddSqlDbContext(configuration);

            services.AddLogging();

            // adding log behaviour pipeline
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehaviour<,>));

            // fixes the issue with google chrome and safari for same site cookies issue
            services.ConfigureNonBreakingSameSiteCookies();

            // identity serve client set up
            services.Setup();
            services.ClientSetup(appSettingConfiguration);

            //Add DIs
            services.RegisterDependencies(configuration);

            services.AddMvc(option => option.EnableEndpointRouting = false);

            // adding static file for spa in production
            services.AddSpaStaticFiles(env);

            services.AddSendGrid(options =>
            {
                options.ApiKey = System.Environment.GetEnvironmentVariable("SENDGRID_API_KEY") ?? appSettingConfiguration.SendGrid.ApiKey;
                options.HttpErrorAsException = true;
            });
        }

        private static void ConfigureApp(WebApplication app)
        {
            var env = app.Environment;
            var tempDataProvider = app.Services.GetRequiredService<ITempDataProvider>();
            var appSettings = app.Services.GetRequiredService<AppSettings>();

            // Log environment information for debugging
            Console.WriteLine($"[STARTUP] Environment: {env.EnvironmentName}");
            Console.WriteLine($"[STARTUP] Is Development: {env.IsDevelopment()}");
            Console.WriteLine($"[STARTUP] Is Production: {env.IsProduction()}");
            Console.WriteLine($"[STARTUP] Content Root: {env.ContentRootPath}");

            app.UseSecurityHeaders();

            app.UseForwardedHeaders();

            app.UseIdentityServer();

            // Check if build files exist to determine production vs development mode
            var buildPath = Path.Join(env.ContentRootPath, Constants.Startup.SpaBuildFolderName);
            var indexPath = Path.Join(buildPath, "index.html");
            var buildExists = Directory.Exists(buildPath);
            var indexExists = File.Exists(indexPath);
            var useProductionMode = buildExists && indexExists;

            // Allow override via environment variable
            var forceProduction = Environment.GetEnvironmentVariable("FORCE_PRODUCTION_MODE") == "true";
            var forceDevelopment = Environment.GetEnvironmentVariable("FORCE_DEVELOPMENT_MODE") == "true";

            if (forceDevelopment)
            {
                useProductionMode = false;
            }
            else if (forceProduction)
            {
                useProductionMode = true;
            }

            //Enable middleware to handle exception
            app.ConfigureExceptionHandler(useProductionMode, tempDataProvider, app.Configuration, appSettings);

            if (useProductionMode)
            {
                Console.WriteLine("[STARTUP] Configuring for Production - Using SPA static files");
                Console.WriteLine($"[STARTUP] Build exists: {buildExists}, Index exists: {indexExists}");
                app.UseSpaStaticFiles();

                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }
            else
            {
                Console.WriteLine("[STARTUP] Configuring for Development - Will use SPA proxy");
                Console.WriteLine($"[STARTUP] Build exists: {buildExists}, Index exists: {indexExists}");
            }

            app.UseHttpsRedirection();
            app.UseAntiXssMiddleware();
            app.UseVirusScan();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseSwaggerDocumentation();

            // Request logging middleware - development only
            if (env.IsDevelopment())
            {
                app.Use(async (context, next) =>
                {
                    Console.WriteLine($"[{context.Request.Method}] {context.Request.Path}");
                    await next.Invoke();
                });
            }
            // mvc and area routing
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(name: "default", pattern: "{controller}/{action}/{id?}");
                endpoints.MapAreaControllerRoute(name: "area", areaName: "idp", pattern: "{area:exists}/{controller}/{action}/{id?}");
            });

            // In development mode use 'npm start' command
            app.UseSpa(env);
        }


        /// <summary>
        /// Builds the Configuration framework
        /// </summary>
        /// <returns></returns>
        private static IConfiguration GetConfiguration()
        {
            IConfigurationBuilder builder = new ConfigurationBuilder();
            builder = LoadConfiguration(builder);

            return builder.Build();
        }


        /// <summary>
        /// Loads configuration information from appSettings.json, configuration.json and environment variables; including environment specific file variants
        /// </summary>
        private static IConfigurationBuilder LoadConfiguration(IConfigurationBuilder builder)
        {
            //CAUTION: Linux file system is case sensitive. So the name of the configuration file must EXACTLY match the value assigned to environment variable.
            string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            builder
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{environment}.json", optional: true, reloadOnChange: true)
                .AddJsonFile("excelsettings.json", optional: false, reloadOnChange: true) //  Adding custom config json file
                .AddEnvironmentVariables();
                
            // Add user secrets in development environment
            if (environment == "Development")
            {
                builder.AddUserSecrets<Program>();
            }

            return builder;
        }

        /// <summary>
        /// Serilog configuration for file and database
        /// </summary>
        /// <param name="host"> Host </param>
        /// <param name="Configuration"> Appsetting configuration </param>
        /// <returns> Logger configuration </returns>
        private static ILogger CreateLogger(IConfiguration configuration)
        {
            var columnOption = new ColumnOptions();
            columnOption.Store.Remove(StandardColumn.MessageTemplate);
            columnOption.Store.Remove(StandardColumn.Properties);
            columnOption.Store.Add(StandardColumn.LogEvent);

            var connectionString = configuration.GetConnectionString(Constants.Startup.ConnectionString);

            return new LoggerConfiguration()
                .Enrich.WithProperty("ApplicationContext", Constants.SeriLog.AppName)
                .WriteTo.Console()
                .WriteTo.File(configuration[Constants.SeriLog.SerilogFilePath] + DateTime.Now.Day + "_" + DateTime.Now.Month + "_" + DateTime.Now.Year + ".log",
                    restrictedToMinimumLevel: LogEventLevel.Information, outputTemplate: configuration[Constants.SeriLog.SerilogOutputFileTemplate])
                .WriteTo.MSSqlServer(connectionString, Constants.SeriLog.LogTableName,
                    restrictedToMinimumLevel: LogEventLevel.Information, columnOptions: columnOption, autoCreateSqlTable: true)
                .ReadFrom.Configuration(configuration)
                .CreateLogger();
        }
    }
}
