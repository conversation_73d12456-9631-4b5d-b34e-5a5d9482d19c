﻿using System;
using System.IO;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SpaServices.ReactDevelopmentServer;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Web.Extensions
{
    internal static class SpaExtension
    {

        internal static IServiceCollection AddSpaStaticFiles(this IServiceCollection services, IWebHostEnvironment env)
        {
            services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = Constants.Startup.SpaBuildFolderName;
            });

            return services;
        }

        internal static IApplicationBuilder UseSpa(this IApplicationBuilder app, IWebHostEnvironment env )
        {
            app.UseSpa(spa =>
            {
                spa.Options.SourcePath = Path.Join(env.ContentRootPath, Constants.Startup.SpaProjectName);

                // Check if build files exist to determine if we should use static files or proxy
                var buildPath = Path.Join(env.ContentRootPath, Constants.Startup.SpaBuildFolderName);
                var indexPath = Path.Join(buildPath, "index.html");
                var buildExists = Directory.Exists(buildPath);
                var indexExists = File.Exists(indexPath);

                // Use static files if build directory and index.html exist, otherwise use proxy
                var useStaticFiles = buildExists && indexExists;

                // Allow override via environment variable for development
                var forceProxy = Environment.GetEnvironmentVariable("USE_SPA_PROXY") == "true";

                if (!useStaticFiles || forceProxy)
                {
                    Console.WriteLine("[SPA] Configuring for Development - Using Vite proxy");
                    Console.WriteLine($"[SPA] Build exists: {buildExists}, Index exists: {indexExists}, Force proxy: {forceProxy}");

                    // Configure for Vite development server
                    spa.Options.StartupTimeout = TimeSpan.FromSeconds(120);

                    // Use generic proxy to Vite dev server instead of React-specific middleware
                    spa.UseProxyToSpaDevelopmentServer("http://localhost:3000");
                }
                else
                {
                    Console.WriteLine("[SPA] Configuring for Production - Serving static files");
                    Console.WriteLine($"[SPA] Build exists: {buildExists}, Index exists: {indexExists}");

                    // Production configuration - serve static files from build folder
                    spa.Options.DefaultPage = "/index.html";
                    spa.Options.DefaultPageStaticFileOptions = new StaticFileOptions()
                    {
                        OnPrepareResponse = context =>
                        {
                            context.Context.Response.Headers["Cache-Control"] = "no-cache, no-store";
                            context.Context.Response.Headers["Expires"] = "-1";
                        }
                    };

                    // Log configuration details for debugging
                    Console.WriteLine($"[SPA] Environment: {env.EnvironmentName}");
                    Console.WriteLine($"[SPA] Content Root: {env.ContentRootPath}");
                    Console.WriteLine($"[SPA] Source Path: {spa.Options.SourcePath}");
                    Console.WriteLine($"[SPA] Build Path: {Constants.Startup.SpaBuildFolderName}");
                    Console.WriteLine($"[SPA] Build directory path: {buildPath}");
                    Console.WriteLine($"[SPA] Index.html path: {indexPath}");
                }
            });

            return app;
        }
    }
}
