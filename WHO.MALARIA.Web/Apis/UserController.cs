using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Features;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Authorize]
    [Route("api/[controller]")]
    public class UserController : BaseApiController
    {
        private readonly IMediator _mediator;
        private readonly IUserQueries _userQueries;

        public UserController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IUserQueries userQueries) : base(mediator, httpContextAccessor)
        {
            _mediator = mediator;
            _userQueries = userQueries;
        }

        /// <summary>
        /// Returns the identity of a user based on the filter criteria passed else returns all
        /// </summary>
        /// <param name="userId">User Id</param>
        /// <returns>List of UserProfileDto objects.</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<UserProfileDto>), 200)]
        [ProducesResponseType(401)]
        [Route("profile/{userId}")]
        public async Task<ActionResult> GetUserProfileAsync(Guid userId)
        {
            return Ok(await QueryAsync(new GetUserProfileByIdQuery(userId)));
        }

        /// <summary>
        /// Change supermanager's status 
        /// </summary>
        /// <param name="command">UpdateUserStatusByWhoUserCommand object</param>
        /// <returns>Response with user Activate or Deactivate operation status</returns>
        [HttpPut]
        [Route("update/status/requester/who")]
        public async Task<ActionResult> UpdateUserStatusByWhoUser(UpdateUserStatusByWhoUserCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isUser = await CommandAsync(command);

            return Ok(isUser);
        }

        /// <summary>
        /// Changes user status and type requested by super manager
        /// </summary>
        /// <param name="command">UpdateUserStautusAndTypeBySuperManagerCommand object</param>
        /// <returns>Response with user Activate or Deactivate operation status</returns>
        [HttpPut]
        [Route("update/statusAndType/requester/supermanager")]
        public async Task<ActionResult> UpdateUserStatusAndTypeBySuperManager(UpdateUserStatusAndTypeBySuperManagerCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isUser = await CommandAsync(command);

            return Ok(isUser);
        }

        /// <summary>
        /// Make viewer as super manager
        /// </summary>
        /// <param name="command">MakeViewerAsSuperManagerCommand object</param>
        /// <returns>Response with user type change status</returns>
        [HttpPut]
        [Route("makeViewerAsSupermanager")]
        public async Task<ActionResult> MakeViewerAsSupermanager(MakeViewerAsSuperManagerCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;

            bool result = await CommandAsync(command);

            return Ok(result);
        }

        /// <summary>
        /// Returns object with the list of user activation requests and user country access pending requests
        /// </summary>
        /// <param name="countryId">Country Id of current user</param>
        /// <returns>Returns PendingRequestsOutputDto object</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PendingRequestsOutputDto), 200)]
        [ProducesResponseType(401)]
        [Route("requests/pending/{countryId}")]
        public async Task<ActionResult> GetPendingRequestsAsync(Guid countryId)
        {
            PendingRequestsOutputDto pendingRequests = await _userQueries.GetPendingRequestsAsync(base.GetCurrentUser().UserId, countryId);

            return Ok(pendingRequests);
        }

        /// <summary>
        /// Registers user with Viewer type
        /// </summary>
        /// <param name="command">Object of CreateIdentityCommand</param>
        /// <returns>Returns IdentityDto object with newly created user details</returns>
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(typeof(IdentityDto), 200)]
        [ProducesResponseType(302)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [Route("register")]
        public async Task<ActionResult> Register([FromBody] CreateIdentityCommand command)
        {
            command.UserType = UserRoleEnum.Viewer;
            command.IsRequestCameFromRegisterUserScreen = true;
            IdentityDto identityDto = await CommandAsync(command);
            return Ok(identityDto);
        }

        /// <summary>
        /// Sends user activation email
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>Returns true if the activation is sent successfully else false</returns>
        [HttpPost]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(302)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [Route("sendActivation")]
        public async Task<ActionResult> SendUserActivationAsync([FromBody] SendUserActivationCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;

            bool success = await CommandAsync(command);

            return Ok(success);
        }

        /// <summary>
        /// Returns users for WHO requestor
        /// </summary>
        /// <returns>Enumerable of UserDto objects</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<UserDto>), 200)]
        [ProducesResponseType(401)]
        [Route("users/requester/who")]
        public async Task<ActionResult> GetUserForWHORequesterAsync()
        {
            IEnumerable<UserDto> users = await _userQueries.GetUsersForWHORequesterAsync(base.GetCurrentUser().UserId);

            return Ok(users);
        }


        /// <summary>
        /// Get newly registered in active viewers
        /// </summary>
        /// <returns>List of newly registered users</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<UserDto>), 200)]
        [ProducesResponseType(401)]
        [Route("new-inactive-viewers")]
        public async Task<ActionResult> GetNewlyRegisteredInActiveViewersAsync()
        {
            IEnumerable<UserDto> users = await _userQueries.GetNewlyRegisteredInActiveViewersAsync(base.GetCurrentUser().UserId);

            return Ok(users);
        }
        /// <summary>
        /// Returns users for SuperManager requestor
        /// </summary>
        /// <param name="countryId">Country Id of current user</param>
        /// <returns>Enumerable of UserDto objects</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<UserDto>), 200)]
        [ProducesResponseType(401)]
        [Route("users/requester/supermanager/{countryId}")]
        public async Task<ActionResult> GetUserForSuperManagerRequesterAsync(Guid countryId)
        {
            IEnumerable<UserDto> users = await _userQueries.GetUsersForSuperManagerRequesterAsync(base.GetCurrentUser().UserId,countryId);

            return Ok(users);
        }

        /// <summary>
        /// Returns all users of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<UserDto>), 200)]
        [ProducesResponseType(401)]
        [Route("users/country/{countryId}")]
        public async Task<ActionResult> GetAllUsersOfCountryAsync(Guid countryId)
        {
            IEnumerable<UserDto> users = await _userQueries.GetAllUsersOfCountryAsync(countryId);

            return Ok(users);
        }

        /// <summary>
        /// Rejects user pending activation request
        /// </summary>
        /// <param name="command">Object of RejectUserActivationRequestCommand</param>
        /// <returns>True when request is rejected successfully; else false</returns>
        [HttpPatch]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(401)]
        [Route("request/useractivation/reject")]
        public async Task<ActionResult> RejectUserActivationRequestAsync(RejectUserActivationRequestCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool success = await CommandAsync(command);

            return Ok(success);
        }

        /// <summary>
        /// Adds and invites new external super manager which does not already exist in WHO active directory
        /// </summary>
        /// <param name="command">Object of CreateExternalSuperManagerCommand</param>
        /// <returns>Newly created user identity</returns>
        [HttpPost]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(401)]
        [Route("create/supermanager/external")]
        public async Task<ActionResult> CreateExternalSuperManagerAsync(CreateExternalSuperManagerCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            IdentityDto identity = await CommandAsync(command);

            return Ok(identity);
        }

        /// <summary>
        /// Adds new internal super manager who already exists in WHO active directory
        /// </summary>
        /// <param name="command">Object of CreateInternalSuperManagerCommand</param>
        /// <returns>True when request is user created successfully; else false</returns>
        [HttpPost]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(401)]
        [Route("create/supermanager/internal")]
        public async Task<ActionResult> CreateInternalSuperManagerAsync(AddExistingUserCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool success = await CommandAsync(command);

            return Ok(success);
        }

        /// <summary>
        /// Updates user details
        /// </summary>
        /// <param name="command">Update user command object</param>
        /// <returns>Returns true if user updated successfully</returns>
        [HttpPut]
        [Route("update")]
        public async Task<ActionResult> UpdateUser(UpdateUserCommand command)
        {
            bool isUser = await _mediator.Send(command);

            //email notification to super manager when access request for country from WHO user
            if (command.UserType == 0 && command.EmailId.Contains("@who.int") && isUser)
            {
                CountryAccessRequestCommand emailCommand = new CountryAccessRequestCommand();
                emailCommand.CountryRequestedForIds = command.CountryRequestedForIds;
                emailCommand.CurrentUserId = command.UserId;
                bool isSuccess = await CommandAsync(emailCommand);
            }

            return Ok(isUser);
        }

        /// <summary>
        /// Approves user country access request
        /// </summary>
        /// <param name="command">Object of ApproveUserCountryAccessCommand</param>
        /// <returns>Returns true if country access request is approved successfully; else false</returns>
        [HttpPost]
        [Route("request/countryaccess/approve")]
        public async Task<ActionResult<bool>> ApproveUserCountryAccess([FromBody] ApproveUserCountryAccessCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        /// <summary>
        /// Rejects user country access request
        /// </summary>
        /// <param name="command">Object of RejectUserCountryAccessCommand</param>
        /// <returns>Returns true if user country access request is rejected successfully; else false</returns>
        [HttpPost]
        [Route("request/countryaccess/reject")]
        public async Task<ActionResult<bool>> RejectUserCountryAccess([FromBody] RejectUserCountryAccessCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        /// <summary>
        /// Rejects user country access request
        /// </summary>
        /// <param name="command">Object of RejectUserCountryAccessCommand</param>
        /// <returns>Returns true if user country access request is rejected successfully; else false</returns>
        [HttpPost]
        [Route("request/countryaccess/updatecomment")]
        public async Task<ActionResult<bool>> UpdateCommentUserCountryAccess([FromBody] UpdateCommentUserCountryAccessCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        /// <summary>
        /// Returns counties based on user access; Retruns all active countries for WHO user type
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdAndNameDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [Route("countries")]
        public async Task<ActionResult> GetCurrentUserCountries()
        {
            IEnumerable<IdAndNameDto> countries = await _userQueries.GetUserCountriesAsync(base.GetCurrentUser());

            return Ok(countries);
        }

        /// <summary>
        /// Returns all managers of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<UserDto>), 200)]
        [ProducesResponseType(401)]
        [Route("managers/{countryId}")]
        public async Task<ActionResult> GetCountryManagersAsync(Guid countryId)
        {
            IEnumerable<UserDto> users = await _userQueries.GetCountryManagersAsync(countryId);

            return Ok(users);
        }

        /// <summary>
        /// Returns all viewers of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<UserDto>), 200)]
        [ProducesResponseType(401)]
        [Route("viewers/{countryId}")]
        public async Task<ActionResult> GetCountryViewersAsync(Guid countryId)
        {
            IEnumerable<UserDto> users = await _userQueries.GetCountryViewersAsync(countryId);

            return Ok(users);
        }

        /// <summary>
        /// Accepts user by making its status active  
        /// </summary>
        /// <returns>true if operation is successful else false</returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("invitation/accept")]
        public async Task<ActionResult> AcceptUserAsync(AcceptUserCommand command)
        {
            bool isUserAccepted = await CommandAsync(command);

            return Ok(isUserAccepted);
        }

        /// <summary>
        /// Resend invitation email to user
        /// </summary>
        /// <param name="command">ResendInvitationCommand object</param>
        /// <returns>True if invitation send is successful else false</returns>
        [HttpPut]
        [Route("resend-invitation")]
        public async Task<ActionResult> ResendInvitationAsync(ResendInvitationCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool status = await CommandAsync(command);

            return Ok(status);
        }

        /// <summary>
        /// Update the default user country status for landing page
        /// </summary>
        /// <param name="command">UpdateDefaultCountryOnLandingPageCommand object</param>
        /// <returns>Return true or false</returns>
        [HttpPatch]
        [Route("update/defaultCountryOnLandingPage")]
        public async Task<ActionResult> UpdateDefaultCountryOnLandingPageAsync([FromBody] UpdateDefaultCountryOnLandingPageCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isUser = await CommandAsync(command);
            return Ok(isUser);
        }

        /// <summary>
        /// Get assigned countries of the user
        /// </summary>      
        /// <returns>List of active countries for the given user</returns>
        [HttpGet]
        [Route("activecountries")]
        public async Task<ActionResult> GetAssignedCountriesOfUserAsync()
        {
            Guid userId = base.GetCurrentUser().UserId;

            IEnumerable<UserCountryDto> users = await _userQueries.GetAssignedCountriesOfUserAsync(userId);

            return Ok(users);
        }

        /// <summary>
        /// Get user profile and countries details
        /// </summary>
        /// <returns>User profile and countries detail</returns>
        [HttpGet]
        [Route("profile/countrydetail")]
        public async Task<ActionResult> GetUserProfileAndCountryDetailsAsync()
        {
            var currentUser = base.GetCurrentUser();
            UserProfileCountryDetailDto userProfileCountryDetail = await _userQueries.GetUserProfileAndCountryDetailsAsync(currentUser.UserId);

            return Ok(userProfileCountryDetail);
        }

        /// <summary>
        ///  Add user country access request
        /// </summary>
        /// <param name="command">AddUserCountryAccessRequestCommand object</param>
        /// <returns>Return true or false</returns>
        [HttpPost]
        [Route("add/countryAccess")]
        public async Task<ActionResult> AddUserCountryAccessRequestAsync(AddUserCountryAccessRequestCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;

            bool isSuccess = await _mediator.Send(command);

            return Ok(isSuccess);
        }
    }
}
