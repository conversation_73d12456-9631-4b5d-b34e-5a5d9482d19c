#!/bin/bash

# Fix Azure Deployment Script
# This script rebuilds the React app and provides deployment instructions

echo "🔧 Fixing Azure Deployment Issue..."
echo "=================================="

# Step 1: Rebuild the React application
echo "📦 Step 1: Rebuilding React application..."
cd WHO.MALARIA.Web/malaria-client

# Check if Node.js 22.15.1 is available
if command -v nvm &> /dev/null; then
    echo "🔄 Switching to Node.js 22.15.1..."
    source ~/.nvm/nvm.sh
    nvm use 22.15.1
fi

echo "🏗️  Building React app for production..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ React build completed successfully!"
else
    echo "❌ React build failed!"
    exit 1
fi

cd ../..

# Step 2: Build .NET application
echo ""
echo "🔨 Step 2: Building .NET application..."
dotnet build WHO.MALARIA.Web/WHO.MALARIA.Web.csproj --configuration Release

if [ $? -eq 0 ]; then
    echo "✅ .NET build completed successfully!"
else
    echo "❌ .NET build failed!"
    exit 1
fi

echo ""
echo "🎉 Build completed successfully!"
echo ""
echo "📋 Next Steps for Azure Deployment:"
echo "===================================="
echo "1. Set Environment Variable in Azure:"
echo "   - Go to Azure Portal → Your App Service → Configuration"
echo "   - Add/Update: ASPNETCORE_ENVIRONMENT = Production"
echo ""
echo "2. Deploy the application:"
echo "   - Use your existing deployment pipeline"
echo "   - Or manually deploy the built files"
echo ""
echo "3. Verify the deployment:"
echo "   - Check Azure logs for '[STARTUP] Environment: Production'"
echo "   - Check for '[SPA] Configuring for Production' messages"
echo ""
echo "🔍 If issues persist, check Azure logs for detailed error messages."
